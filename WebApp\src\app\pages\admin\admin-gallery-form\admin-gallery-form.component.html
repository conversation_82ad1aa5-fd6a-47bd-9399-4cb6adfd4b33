<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">
        {{ isEditMode ? 'Edit Gallery Image' : 'Add New Gallery Image' }}
      </h1>
      <p class="text-gray-600">
        {{ isEditMode ? 'Update gallery image information' : 'Add a new image to your gallery' }}
      </p>
    </div>
    <button (click)="cancel()" 
            class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
      <i class="fas fa-arrow-left mr-2"></i>
      Back to Gallery
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Gallery Form -->
  <form *ngIf="!isLoading" [formGroup]="galleryForm" (ngSubmit)="onSubmit()" class="space-y-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Information -->
      <div class="lg:col-span-2 space-y-6">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Image Information</h3>
          
          <!-- Image Title -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Image Title *</label>
            <input
              type="text"
              formControlName="title"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('title')"
              placeholder="Enter image title"
            />
            <div *ngIf="isFieldInvalid('title')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('title') }}
            </div>
          </div>

          <!-- Description -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
            <textarea
              formControlName="description"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('description')"
              placeholder="Describe the artwork, its significance, and techniques used..."
            ></textarea>
            <div *ngIf="isFieldInvalid('description')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('description') }}
            </div>
          </div>

          <!-- Image URL -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Image URL *</label>
            <input
              type="url"
              formControlName="image"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('image')"
              placeholder="https://example.com/image.jpg"
            />
            <div *ngIf="isFieldInvalid('image')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('image') }}
            </div>
            <p class="mt-1 text-sm text-gray-500">Provide a direct URL to the image file</p>
          </div>

          <!-- Artist Name -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Artist Name *</label>
            <input
              type="text"
              formControlName="artist"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('artist')"
              placeholder="Enter artist name"
            />
            <div *ngIf="isFieldInvalid('artist')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('artist') }}
            </div>
          </div>

          <!-- Category -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
            <select
              formControlName="category"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('category')"
            >
              <option value="">Select a category</option>
              <option *ngFor="let category of categories" [value]="category">
                {{ category }}
              </option>
            </select>
            <div *ngIf="isFieldInvalid('category')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('category') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Image Preview -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
          
          <div class="space-y-4">
            <!-- Image Preview -->
            <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
              <img 
                *ngIf="imagePreviewUrl" 
                [src]="imagePreviewUrl" 
                [alt]="galleryForm.get('title')?.value"
                class="w-full h-full object-cover"
                (error)="onImageError($event)"
              />
              <div *ngIf="!imagePreviewUrl" 
                   class="w-full h-full flex items-center justify-center text-gray-400">
                <div class="text-center">
                  <i class="fas fa-image text-4xl mb-2"></i>
                  <p class="text-sm">Image preview will appear here</p>
                </div>
              </div>
            </div>
            
            <!-- Image Details -->
            <div class="space-y-2">
              <h4 class="font-semibold text-gray-900">
                {{ galleryForm.get('title')?.value || 'Image Title' }}
              </h4>
              
              <p class="text-sm text-gray-600">
                by {{ galleryForm.get('artist')?.value || 'Artist Name' }}
              </p>
              
              <span *ngIf="galleryForm.get('category')?.value" 
                    class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                {{ galleryForm.get('category')?.value }}
              </span>
              
              <p class="text-sm text-gray-600 mt-2">
                {{ galleryForm.get('description')?.value || 'Image description will appear here...' }}
              </p>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="space-y-3">
            <button
              type="submit"
              [disabled]="isSubmitting || galleryForm.invalid"
              class="w-full flex items-center justify-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <span *ngIf="isSubmitting" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
              {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Image' : 'Add to Gallery') }}
            </button>
            
            <button
              type="button"
              (click)="cancel()"
              class="w-full px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </div>

        <!-- Guidelines -->
        <div class="bg-blue-50 rounded-lg p-4">
          <h4 class="text-sm font-semibold text-blue-900 mb-2">Image Guidelines</h4>
          <ul class="text-sm text-blue-800 space-y-1">
            <li>• Use high-resolution images (minimum 800x800px)</li>
            <li>• Ensure images are properly lit and in focus</li>
            <li>• Include detailed descriptions for better engagement</li>
            <li>• Credit the artist accurately</li>
            <li>• Choose appropriate categories for easy browsing</li>
          </ul>
        </div>
      </div>
    </div>
  </form>
</div>
