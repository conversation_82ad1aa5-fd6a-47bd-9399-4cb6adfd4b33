import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AdminApiService } from '../../../services/admin-api.service';
import { ApiGalleryImage } from '../../../services/api.service';

@Component({
  selector: 'app-admin-gallery-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './admin-gallery-form.component.html',
  styleUrl: './admin-gallery-form.component.css'
})
export class AdminGalleryFormComponent implements OnInit {
  galleryForm: FormGroup;
  isLoading = false;
  isSubmitting = false;
  error = '';
  isEditMode = false;
  imageId: number | null = null;

  categories = [
    'Paintings',
    'Clay Crafts',
    'Textiles',
    'Wood Crafts',
    'Sculptures',
    'Traditional Art',
    'Contemporary Art'
  ];

  constructor(
    private fb: FormBuilder,
    private adminApi: AdminApiService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.galleryForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      image: ['', [Validators.required]],
      artist: ['', [Validators.required]],
      category: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.imageId = +params['id'];
        this.loadGalleryImage(this.imageId);
      }
    });
  }

  loadGalleryImage(id: number): void {
    this.isLoading = true;
    this.adminApi.getGalleryImages().subscribe({
      next: (response) => {
        const image = response.results?.find(img => img.id === id);
        if (image) {
          this.populateForm(image);
        } else {
          this.error = 'Gallery image not found';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load gallery image';
        this.isLoading = false;
        console.error('Error loading gallery image:', error);
      }
    });
  }

  populateForm(image: ApiGalleryImage): void {
    this.galleryForm.patchValue({
      title: image.title,
      description: image.description,
      image: image.image,
      artist: image.artist,
      category: image.category
    });
  }

  onSubmit(): void {
    if (this.galleryForm.valid) {
      this.isSubmitting = true;
      this.error = '';

      const formData = this.prepareFormData();

      const operation = this.isEditMode 
        ? this.adminApi.updateGalleryImage(this.imageId!, formData)
        : this.adminApi.createGalleryImage(formData);

      operation.subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.router.navigate(['/admin/gallery']);
        },
        error: (error) => {
          this.isSubmitting = false;
          this.error = 'Failed to save gallery image. Please try again.';
          console.error('Error saving gallery image:', error);
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  prepareFormData(): any {
    const formValue = this.galleryForm.value;
    return {
      title: formValue.title,
      description: formValue.description,
      image: formValue.image,
      artist: formValue.artist,
      category: formValue.category
    };
  }

  markFormGroupTouched(): void {
    Object.keys(this.galleryForm.controls).forEach(key => {
      const control = this.galleryForm.get(key);
      control?.markAsTouched();
    });
  }

  cancel(): void {
    this.router.navigate(['/admin/gallery']);
  }

  // Validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.galleryForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.galleryForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['minlength']) return `${fieldName} is too short`;
    }
    return '';
  }

  // Image preview
  get imagePreviewUrl(): string {
    return this.galleryForm.get('image')?.value || '';
  }

  onImageError(event: any): void {
    event.target.src = '/assets/placeholder-image.jpg';
  }
}
