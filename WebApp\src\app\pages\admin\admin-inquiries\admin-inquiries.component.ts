import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AdminApiService } from '../../../services/admin-api.service';
import { ApiContactInquiry } from '../../../services/api.service';

@Component({
  selector: 'app-admin-inquiries',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './admin-inquiries.component.html',
  styleUrl: './admin-inquiries.component.css'
})
export class AdminInquiriesComponent implements OnInit {
  inquiries: ApiContactInquiry[] = [];
  isLoading = true;
  error = '';
  searchTerm = '';
  selectedStatus = '';
  
  statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'resolved', label: 'Resolved' },
    { value: 'closed', label: 'Closed' }
  ];

  constructor(private adminApi: AdminApiService) {}

  ngOnInit(): void {
    this.loadInquiries();
  }

  loadInquiries(): void {
    this.isLoading = true;
    this.adminApi.getContactInquiries().subscribe({
      next: (response) => {
        this.inquiries = response.results || [];
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load inquiries';
        this.isLoading = false;
        console.error('Inquiries error:', error);
      }
    });
  }

  updateInquiryStatus(inquiryId: number, newStatus: string): void {
    this.adminApi.updateInquiryStatus(inquiryId, newStatus).subscribe({
      next: (updatedInquiry) => {
        const index = this.inquiries.findIndex(i => i.id === inquiryId);
        if (index !== -1) {
          this.inquiries[index] = updatedInquiry;
        }
      },
      error: (error) => {
        alert('Failed to update inquiry status');
        console.error('Update error:', error);
      }
    });
  }

  get filteredInquiries(): ApiContactInquiry[] {
    return this.inquiries.filter(inquiry => {
      const matchesSearch = !this.searchTerm || 
        inquiry.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        inquiry.email.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        inquiry.subject.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        inquiry.message.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesStatus = !this.selectedStatus || inquiry.status === this.selectedStatus;
      
      return matchesSearch && matchesStatus;
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  refreshInquiries(): void {
    this.loadInquiries();
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  truncateMessage(message: string, length: number = 100): string {
    return message.length > length ? message.substring(0, length) + '...' : message;
  }

  // Helper methods for statistics
  getPendingCount(): number {
    return this.inquiries.filter(i => i.status === 'pending').length;
  }

  getInProgressCount(): number {
    return this.inquiries.filter(i => i.status === 'in_progress').length;
  }

  getResolvedCount(): number {
    return this.inquiries.filter(i => i.status === 'resolved').length;
  }

  getTotalCount(): number {
    return this.inquiries.length;
  }
}
