@echo off
echo Starting <PERSON><PERSON><PERSON> Application...

echo.
echo Creating admin user...
cd mithlani_backend
python manage.py create_admin_user --username admin --password admin123 --email <EMAIL> --full-name "Admin User"

echo.
echo Starting Django backend server...
start "Django Backend" cmd /k "python manage.py runserver"

echo.
echo Waiting for backend to start...
timeout /t 5

echo.
echo Starting Angular frontend server...
cd ..\WebApp
start "Angular Frontend" cmd /k "ng serve"

echo.
echo Both servers are starting...
echo Backend: http://localhost:8000
echo Frontend: http://localhost:4200
echo Admin Login: http://localhost:4200/admin/login
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
pause
