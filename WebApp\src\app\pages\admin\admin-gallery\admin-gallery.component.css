/* Gallery grid styles */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.gallery-item {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.2s ease;
}

.gallery-item:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

/* Image container */
.image-container {
  aspect-ratio: 1;
  background: #f3f4f6;
  overflow: hidden;
  position: relative;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
  transform: scale(1.05);
}

/* Image overlay for actions */
.image-overlay {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 0 0 0 0.5rem;
  padding: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.gallery-item:hover .image-overlay {
  opacity: 1;
}

.overlay-action {
  color: white;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
  margin-left: 0.25rem;
}

.overlay-action:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Content area */
.gallery-content {
  padding: 1rem;
}

.gallery-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gallery-artist {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.gallery-category {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 9999px;
  background: #dbeafe;
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.gallery-description {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.gallery-meta {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #f3f4f6;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 0.25rem;
  margin-left: 0.5rem;
}

.action-btn {
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  color: #6b7280;
}

.action-btn:hover {
  transform: translateY(-1px);
}

.action-btn.edit:hover {
  color: #ea580c;
  background-color: #fed7aa;
}

.action-btn.delete:hover {
  color: #ef4444;
  background-color: #fee2e2;
}

/* Filter section */
.filter-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-field {
  display: flex;
  flex-direction: column;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.filter-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.filter-input:focus {
  outline: none;
  border-color: #ea580c;
  box-shadow: 0 0 0 3px rgba(234, 88, 12, 0.1);
}

/* Results info */
.results-info {
  display: flex;
  align-items: end;
  font-size: 0.875rem;
  color: #6b7280;
}

/* Loading states */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #ea580c;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 3rem 0;
}

.empty-icon {
  font-size: 4rem;
  color: #d1d5db;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.5rem;
}

.empty-description {
  color: #6b7280;
  margin-bottom: 1rem;
}

.empty-action {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #ea580c;
  color: white;
  border-radius: 0.375rem;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.empty-action:hover {
  background-color: #dc2626;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
  }
  
  .filter-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .header-actions {
    justify-content: stretch;
  }
  
  .header-actions > * {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
  }
}

/* Bulk actions */
.bulk-actions {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.bulk-actions-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bulk-info {
  font-size: 0.875rem;
  color: #6b7280;
}

.bulk-buttons {
  display: flex;
  gap: 0.5rem;
}

.bulk-btn {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  color: #6b7280;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  text-decoration: none;
}

.bulk-btn:hover {
  color: #111827;
  background-color: #f3f4f6;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
