import hashlib
import secrets
from datetime import datetime, timedelta
from django.utils import timezone
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, BasePermission
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count
from .models import (
    AdminUser, AdminSession, SocialMediaLink, Artist, ArtistSpotlight, TeamMember, 
    Category, Product, GalleryImage, FAQ, Testimonial, CompanyFeature, CompanyInfo, 
    Achievement, Facility, Order, ContactInquiry, ArtistApplication
)
from .serializers import (
    AdminUserSerializer, AdminLoginSerializer, AdminSessionSerializer,
    SocialMediaLinkSerializer, ArtistSerializer, ArtistSpotlightSerializer,
    TeamMemberSerializer, CategorySerializer, ProductSerializer, GalleryImageSerializer,
    FAQSerializer, TestimonialSerializer, CompanyFeatureSerializer, CompanyInfoSerializer,
    AchievementSerializer, FacilitySerializer, OrderSerializer,
    ContactInquirySerializer, ArtistApplicationSerializer
)


class AdminJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication for admin users
    """
    def get_user(self, validated_token):
        try:
            admin_user_id = validated_token.get('admin_user_id')
            if admin_user_id:
                return AdminUser.objects.get(id=admin_user_id, is_active=True)
        except AdminUser.DoesNotExist:
            pass
        return None

class IsAdminAuthenticated(BasePermission):
    """
    Custom permission to check if user is authenticated admin with valid session or JWT
    """
    def has_permission(self, request, view):
        # Try JWT authentication first
        auth_header = request.headers.get('Authorization', '')
        if auth_header.startswith('Bearer '):
            token = auth_header.replace('Bearer ', '')
            try:
                # Try to decode JWT token
                from rest_framework_simplejwt.tokens import UntypedToken
                from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
                from jwt import decode as jwt_decode
                from django.conf import settings

                UntypedToken(token)  # Validate token
                payload = jwt_decode(token, settings.SECRET_KEY, algorithms=['HS256'])
                admin_user_id = payload.get('admin_user_id')

                if admin_user_id:
                    admin_user = AdminUser.objects.get(id=admin_user_id, is_active=True)
                    request.admin_user = admin_user
                    return True
            except (InvalidToken, TokenError, AdminUser.DoesNotExist, Exception):
                pass

        # Fallback to session-based authentication
        session_token = request.COOKIES.get('admin_session_token')
        if not session_token and auth_header.startswith('Bearer '):
            session_token = auth_header.replace('Bearer ', '')

        if session_token:
            try:
                session = AdminSession.objects.get(
                    session_token=session_token,
                    is_active=True,
                    expires_at__gt=timezone.now()
                )
                request.admin_user = session.admin_user
                return True
            except AdminSession.DoesNotExist:
                pass

        return False


@method_decorator(csrf_exempt, name='dispatch')
class AdminAuthViewSet(viewsets.ViewSet):
    """Admin authentication endpoints"""
    permission_classes = [AllowAny]

    @action(detail=False, methods=['post'])
    def login(self, request):
        """Admin login"""
        serializer = AdminLoginSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({'error': 'Invalid data'}, status=status.HTTP_400_BAD_REQUEST)

        username = serializer.validated_data['username']
        password = serializer.validated_data['password']
        
        # Hash the password for comparison
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        try:
            admin_user = AdminUser.objects.get(
                username=username, 
                password_hash=password_hash, 
                is_active=True
            )
        except AdminUser.DoesNotExist:
            return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

        # Create session
        session_token = secrets.token_urlsafe(32)
        expires_at = timezone.now() + timedelta(hours=24)  # 24 hour session
        
        # Deactivate old sessions
        AdminSession.objects.filter(admin_user=admin_user, is_active=True).update(is_active=False)
        
        # Create new session
        session = AdminSession.objects.create(
            admin_user=admin_user,
            session_token=session_token,
            expires_at=expires_at
        )
        
        # Update last login
        admin_user.last_login = timezone.now()
        admin_user.save()

        # Create JWT tokens
        refresh = RefreshToken()
        refresh['admin_user_id'] = admin_user.id
        refresh['username'] = admin_user.username
        refresh['is_super_admin'] = admin_user.is_super_admin

        access_token = refresh.access_token
        access_token['admin_user_id'] = admin_user.id
        access_token['username'] = admin_user.username
        access_token['is_super_admin'] = admin_user.is_super_admin

        response = Response({
            'access_token': str(access_token),
            'refresh_token': str(refresh),
            'session_token': session_token,  # Keep for backward compatibility
            'expires_at': expires_at,
            'admin_user': AdminUserSerializer(admin_user).data
        })

        # Set HTTP-only cookie for better security
        response.set_cookie(
            'admin_session_token',
            session_token,
            max_age=24 * 60 * 60,  # 24 hours in seconds
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite='Lax'
        )

        # Also set JWT token in cookie
        response.set_cookie(
            'admin_access_token',
            str(access_token),
            max_age=24 * 60 * 60,  # 24 hours in seconds
            httponly=True,
            secure=False,
            samesite='Lax'
        )

        return response

    @action(detail=False, methods=['post'])
    def logout(self, request):
        """Admin logout"""
        # Get session token from cookie or header
        session_token = request.COOKIES.get('admin_session_token')
        if not session_token:
            session_token = request.headers.get('Authorization', '').replace('Bearer ', '')

        if session_token:
            AdminSession.objects.filter(session_token=session_token).update(is_active=False)

        response = Response({'message': 'Logged out successfully'})
        # Clear the cookies
        response.delete_cookie('admin_session_token')
        response.delete_cookie('admin_access_token')
        return response

    @action(detail=False, methods=['get'])
    def verify(self, request):
        """Verify admin session"""
        # Get session token from cookie or header
        session_token = request.COOKIES.get('admin_session_token')
        if not session_token:
            session_token = request.headers.get('Authorization', '').replace('Bearer ', '')

        if not session_token:
            return Response({'error': 'No session token'}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            session = AdminSession.objects.get(
                session_token=session_token,
                is_active=True,
                expires_at__gt=timezone.now()
            )
            return Response(AdminSessionSerializer(session).data)
        except AdminSession.DoesNotExist:
            return Response({'error': 'Invalid or expired session'}, status=status.HTTP_401_UNAUTHORIZED)

    @action(detail=False, methods=['post'])
    def refresh(self, request):
        """Refresh JWT token"""
        refresh_token = request.data.get('refresh_token')
        if not refresh_token:
            return Response({'error': 'Refresh token required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            refresh = RefreshToken(refresh_token)
            admin_user_id = refresh.get('admin_user_id')

            if not admin_user_id:
                return Response({'error': 'Invalid refresh token'}, status=status.HTTP_401_UNAUTHORIZED)

            admin_user = AdminUser.objects.get(id=admin_user_id, is_active=True)

            # Create new access token
            new_refresh = RefreshToken()
            new_refresh['admin_user_id'] = admin_user.id
            new_refresh['username'] = admin_user.username
            new_refresh['is_super_admin'] = admin_user.is_super_admin

            access_token = new_refresh.access_token
            access_token['admin_user_id'] = admin_user.id
            access_token['username'] = admin_user.username
            access_token['is_super_admin'] = admin_user.is_super_admin

            return Response({
                'access_token': str(access_token),
                'refresh_token': str(new_refresh),
                'admin_user': AdminUserSerializer(admin_user).data
            })

        except Exception as e:
            return Response({'error': 'Invalid refresh token'}, status=status.HTTP_401_UNAUTHORIZED)


class AdminDashboardViewSet(viewsets.ViewSet):
    """Admin dashboard statistics and overview"""
    permission_classes = [IsAdminAuthenticated]

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get dashboard statistics"""
        stats = {
            'total_products': Product.objects.count(),
            'total_artists': Artist.objects.count(),
            'total_orders': Order.objects.count(),
            'pending_inquiries': ContactInquiry.objects.filter(status='pending').count(),
            'pending_applications': ArtistApplication.objects.filter(status='pending').count(),
            'total_gallery_images': GalleryImage.objects.count(),
            'total_testimonials': Testimonial.objects.count(),
            'recent_orders': OrderSerializer(
                Order.objects.order_by('-created_at')[:5], many=True
            ).data,
            'recent_inquiries': ContactInquirySerializer(
                ContactInquiry.objects.order_by('-created_at')[:5], many=True
            ).data,
            'recent_applications': ArtistApplicationSerializer(
                ArtistApplication.objects.order_by('-created_at')[:5], many=True
            ).data
        }
        return Response(stats)


# Admin CRUD ViewSets with full permissions
class AdminSocialMediaLinkViewSet(viewsets.ModelViewSet):
    queryset = SocialMediaLink.objects.all()
    serializer_class = SocialMediaLinkSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminArtistViewSet(viewsets.ModelViewSet):
    queryset = Artist.objects.all()
    serializer_class = ArtistSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminArtistSpotlightViewSet(viewsets.ModelViewSet):
    queryset = ArtistSpotlight.objects.all()
    serializer_class = ArtistSpotlightSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminTeamMemberViewSet(viewsets.ModelViewSet):
    queryset = TeamMember.objects.all()
    serializer_class = TeamMemberSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminCategoryViewSet(viewsets.ModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [IsAdminAuthenticated]


class AdminProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminGalleryImageViewSet(viewsets.ModelViewSet):
    queryset = GalleryImage.objects.all()
    serializer_class = GalleryImageSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminFAQViewSet(viewsets.ModelViewSet):
    queryset = FAQ.objects.all()
    serializer_class = FAQSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminTestimonialViewSet(viewsets.ModelViewSet):
    queryset = Testimonial.objects.all()
    serializer_class = TestimonialSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminCompanyFeatureViewSet(viewsets.ModelViewSet):
    queryset = CompanyFeature.objects.all()
    serializer_class = CompanyFeatureSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminCompanyInfoViewSet(viewsets.ModelViewSet):
    queryset = CompanyInfo.objects.all()
    serializer_class = CompanyInfoSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminAchievementViewSet(viewsets.ModelViewSet):
    queryset = Achievement.objects.all()
    serializer_class = AchievementSerializer
    permission_classes = [IsAdminAuthenticated]


class AdminFacilityViewSet(viewsets.ModelViewSet):
    queryset = Facility.objects.all()
    serializer_class = FacilitySerializer
    permission_classes = [IsAdminAuthenticated]


class AdminOrderViewSet(viewsets.ModelViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [IsAdminAuthenticated]

    @action(detail=True, methods=['patch'])
    def update_status(self, request, pk=None):
        """Update order status"""
        order = self.get_object()
        new_status = request.data.get('status')
        if new_status in ['pending', 'confirmed', 'shipped', 'delivered', 'cancelled']:
            order.status = new_status
            order.save()
            return Response(self.get_serializer(order).data)
        return Response({'error': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)


class AdminContactInquiryViewSet(viewsets.ModelViewSet):
    queryset = ContactInquiry.objects.all()
    serializer_class = ContactInquirySerializer
    permission_classes = [IsAdminAuthenticated]

    @action(detail=True, methods=['patch'])
    def update_status(self, request, pk=None):
        """Update inquiry status"""
        inquiry = self.get_object()
        new_status = request.data.get('status')
        if new_status in ['pending', 'in_progress', 'resolved', 'closed']:
            inquiry.status = new_status
            inquiry.save()
            return Response(self.get_serializer(inquiry).data)
        return Response({'error': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)


class AdminArtistApplicationViewSet(viewsets.ModelViewSet):
    queryset = ArtistApplication.objects.all()
    serializer_class = ArtistApplicationSerializer
    permission_classes = [IsAdminAuthenticated]

    @action(detail=True, methods=['patch'])
    def update_status(self, request, pk=None):
        """Update application status"""
        application = self.get_object()
        new_status = request.data.get('status')
        admin_notes = request.data.get('admin_notes', '')
        
        if new_status in ['pending', 'approved', 'rejected', 'under_review']:
            application.status = new_status
            if admin_notes:
                application.admin_notes = admin_notes
            application.save()
            return Response(self.get_serializer(application).data)
        return Response({'error': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)
