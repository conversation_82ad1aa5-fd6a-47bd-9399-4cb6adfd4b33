import hashlib
from django.core.management.base import BaseCommand
from mithlani_api.models import AdminUser


class Command(BaseCommand):
    help = 'Create an admin user for the admin panel'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Admin username', default='admin')
        parser.add_argument('--password', type=str, help='Admin password', default='admin123')
        parser.add_argument('--email', type=str, help='Admin email', default='<EMAIL>')
        parser.add_argument('--full-name', type=str, help='Admin full name', default='Admin User')

    def handle(self, *args, **options):
        username = options['username']
        password = options['password']
        email = options['email']
        full_name = options['full_name']

        # Check if admin user already exists
        if AdminUser.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.WARNING(f'Admin user "{username}" already exists')
            )
            return

        # Hash the password
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        # Create admin user
        admin_user = AdminUser.objects.create(
            username=username,
            email=email,
            full_name=full_name,
            password_hash=password_hash,
            is_active=True,
            is_super_admin=True
        )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created admin user "{username}" with password "{password}"'
            )
        )
        self.stdout.write(
            self.style.SUCCESS(
                f'You can now login to the admin panel at /admin/login'
            )
        )
