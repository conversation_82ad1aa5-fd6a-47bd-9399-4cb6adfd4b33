/* Form styling */
.form-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

/* Input focus states */
.form-input:focus {
  outline: none;
  border-color: #ea580c;
  box-shadow: 0 0 0 3px rgba(234, 88, 12, 0.1);
}

/* Error states */
.form-input.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Button states */
.btn-primary {
  background-color: #ea580c;
  color: white;
  transition: background-color 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  background-color: #dc2626;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
  transition: background-color 0.2s ease;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

/* Form array styling */
.form-array-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.form-array-input {
  flex: 1;
}

.form-array-remove {
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.form-array-remove:hover:not(:disabled) {
  background-color: #dc2626;
}

.form-array-remove:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-array-add {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-array-add:hover {
  background-color: #2563eb;
}

/* Checkbox styling */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-item input[type="checkbox"] {
  border-radius: 0.25rem;
  border: 1px solid #d1d5db;
  color: #ea580c;
}

.checkbox-item input[type="checkbox"]:focus {
  border-color: #ea580c;
  box-shadow: 0 0 0 3px rgba(234, 88, 12, 0.1);
}

/* Loading states */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}

/* Validation styling */
.field-valid {
  border-color: #10b981;
}

.field-invalid {
  border-color: #ef4444;
}

/* Help text */
.help-text {
  color: #6b7280;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Form sections */
.form-section-header {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}
