# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-22 17:30+0100\n"
"Last-Translator: Stéphane Malta e Sousa <<EMAIL>>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"L'en-tête 'Authorization' doit contenir deux valeurs séparées par des espaces"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Le type de jeton fourni n'est pas valide"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr ""
"Le jeton ne contient aucune information permettant d'identifier l'utilisateur"

#: authentication.py:132
msgid "User not found"
msgstr "L'utilisateur n'a pas été trouvé"

#: authentication.py:135
msgid "User is inactive"
msgstr "L'utilisateur est désactivé"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Type d'algorithme non reconnu '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "Vous devez installer cryptography afin d'utiliser {}."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Le jeton est invalide ou expiré"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "L'algorithme spécifié est invalide"

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Le jeton est invalide ou expiré"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Le jeton est invalide ou expiré"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Aucun compte actif n'a été trouvé avec les identifiants fournis"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Aucun compte actif n'a été trouvé avec les identifiants fournis"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Le paramètre '{}' a été supprimé. Voir '{}' pour la liste des paramètres "
"disponibles."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "Utilisateur"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "Créé le"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "Expire le"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Liste des jetons bannis"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Ne peut pas créer de jeton sans type ni durée de vie"

#: tokens.py:126
msgid "Token has no id"
msgstr "Le jeton n'a pas d'id"

#: tokens.py:138
msgid "Token has no type"
msgstr "Le jeton n'a pas de type"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Le jeton a un type erroné"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Le jeton n'a pas le privilège '{}'"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "Le privilège '{}' du jeton a expiré"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Le jeton a été banni"
