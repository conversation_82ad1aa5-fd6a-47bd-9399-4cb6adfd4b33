<div class="space-y-8">
  <!-- <PERSON> Header -->
  <div>
    <h1 class="text-2xl font-bold text-gray-900">Company Information</h1>
    <p class="text-gray-600">Manage company details, features, achievements, and facilities</p>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <div *ngIf="!isLoading && !error" class="space-y-8">
    <!-- Company Info Section -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900">Basic Information</h2>
        <a *ngIf="companyInfo" [routerLink]="['/admin/company/info', companyInfo.id, 'edit']" 
           class="text-blue-600 hover:text-blue-700 font-medium">
          <i class="fas fa-edit mr-1"></i>
          Edit
        </a>
        <a *ngIf="!companyInfo" routerLink="/admin/company/info/new" 
           class="text-orange-600 hover:text-orange-700 font-medium">
          <i class="fas fa-plus mr-1"></i>
          Add Company Info
        </a>
      </div>
      
      <div *ngIf="companyInfo" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="font-semibold text-gray-900 mb-2">{{ companyInfo.name }}</h3>
          <p class="text-gray-600 mb-4">{{ companyInfo.tagline }}</p>
          <p class="text-gray-700">{{ companyInfo.description }}</p>
        </div>
        <div>
          <h4 class="font-medium text-gray-900 mb-2">Mission</h4>
          <p class="text-gray-700">{{ companyInfo.mission }}</p>
        </div>
      </div>
      
      <div *ngIf="!companyInfo" class="text-center py-8">
        <p class="text-gray-500 mb-4">No company information found</p>
        <a routerLink="/admin/company/info/new" 
           class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700">
          <i class="fas fa-plus mr-2"></i>
          Add Company Info
        </a>
      </div>
    </div>

    <!-- Company Features Section -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900">Features</h2>
        <a routerLink="/admin/company/features/new" 
           class="text-orange-600 hover:text-orange-700 font-medium">
          <i class="fas fa-plus mr-1"></i>
          Add Feature
        </a>
      </div>
      
      <div *ngIf="companyFeatures.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div *ngFor="let feature of companyFeatures" class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="font-semibold text-gray-900 mb-2">{{ feature.title }}</h3>
              <p class="text-gray-600 text-sm">{{ feature.description }}</p>
            </div>
            <div class="flex items-center space-x-2 ml-2">
              <a [routerLink]="['/admin/company/features', feature.id, 'edit']" 
                 class="text-blue-600 hover:text-blue-700 text-sm">
                <i class="fas fa-edit"></i>
              </a>
              <button (click)="deleteFeature(feature.id)" 
                      class="text-red-600 hover:text-red-700 text-sm">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div *ngIf="companyFeatures.length === 0" class="text-center py-8">
        <p class="text-gray-500 mb-4">No features found</p>
        <a routerLink="/admin/company/features/new" 
           class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700">
          <i class="fas fa-plus mr-2"></i>
          Add Feature
        </a>
      </div>
    </div>

    <!-- Achievements Section -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900">Achievements</h2>
        <a routerLink="/admin/company/achievements/new" 
           class="text-orange-600 hover:text-orange-700 font-medium">
          <i class="fas fa-plus mr-1"></i>
          Add Achievement
        </a>
      </div>
      
      <div *ngIf="achievements.length > 0" class="space-y-4">
        <div *ngFor="let achievement of achievements" class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center mb-2">
                <span class="bg-orange-100 text-orange-800 text-sm font-medium px-2 py-1 rounded">
                  {{ achievement.year }}
                </span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">{{ achievement.title }}</h3>
              <p class="text-gray-600">{{ achievement.description }}</p>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <a [routerLink]="['/admin/company/achievements', achievement.id, 'edit']" 
                 class="text-blue-600 hover:text-blue-700">
                <i class="fas fa-edit"></i>
              </a>
              <button (click)="deleteAchievement(achievement.id)" 
                      class="text-red-600 hover:text-red-700">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div *ngIf="achievements.length === 0" class="text-center py-8">
        <p class="text-gray-500 mb-4">No achievements found</p>
        <a routerLink="/admin/company/achievements/new" 
           class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700">
          <i class="fas fa-plus mr-2"></i>
          Add Achievement
        </a>
      </div>
    </div>

    <!-- Facilities Section -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900">Facilities</h2>
        <a routerLink="/admin/company/facilities/new" 
           class="text-orange-600 hover:text-orange-700 font-medium">
          <i class="fas fa-plus mr-1"></i>
          Add Facility
        </a>
      </div>
      
      <div *ngIf="facilities.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div *ngFor="let facility of facilities" class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-start justify-between mb-4">
            <h3 class="font-semibold text-gray-900">{{ facility.name }}</h3>
            <div class="flex items-center space-x-2">
              <a [routerLink]="['/admin/company/facilities', facility.id, 'edit']" 
                 class="text-blue-600 hover:text-blue-700">
                <i class="fas fa-edit"></i>
              </a>
              <button (click)="deleteFacility(facility.id)" 
                      class="text-red-600 hover:text-red-700">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          <p class="text-gray-600 mb-3">{{ facility.description }}</p>
          <div *ngIf="facility.features && facility.features.length > 0">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Features:</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li *ngFor="let feature of facility.features" class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                {{ feature }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <div *ngIf="facilities.length === 0" class="text-center py-8">
        <p class="text-gray-500 mb-4">No facilities found</p>
        <a routerLink="/admin/company/facilities/new" 
           class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700">
          <i class="fas fa-plus mr-2"></i>
          Add Facility
        </a>
      </div>
    </div>
  </div>
</div>
