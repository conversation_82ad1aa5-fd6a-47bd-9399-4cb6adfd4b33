# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2019.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-22 17:30+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "Autorizační hlavička musí obsahovat dvě hodnoty oddělené mezerou"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Daný token není validní pro žádný typ tokenu"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Token neobsahoval žádnou rozpoznatelnou identifikaci uživatele"

#: authentication.py:132
msgid "User not found"
msgstr "Uživatel nenalezen"

#: authentication.py:135
msgid "User is inactive"
msgstr "Uživatel není aktivní"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Nerozpoznaný typ algoritmu '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr ""

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Token není validní nebo vypršela jeho platnost"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr ""

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Token není validní nebo vypršela jeho platnost"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Token není validní nebo vypršela jeho platnost"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Žádný aktivní účet s danými údaji nebyl nalezen"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Žádný aktivní účet s danými údaji nebyl nalezen"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr "Nastavení '{}' bylo odstraněno. Dostupná nastavení jsou v '{}'"

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "uživatel"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "vytvořený v"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "platí do"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token Blacklist"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Nelze vytvořit token bez zadaného typu nebo životnosti"

#: tokens.py:126
msgid "Token has no id"
msgstr "Token nemá žádný identifikátor"

#: tokens.py:138
msgid "Token has no type"
msgstr "Token nemá žádný typ"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Token má špatný typ"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Token nemá žádnou hodnotu '{}'"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "Hodnota tokenu '{}' vypršela"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Token je na černé listině"
