import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AdminApiService } from '../../../services/admin-api.service';
import { ApiArtist, ApiSocialMediaLink } from '../../../services/api.service';

@Component({
  selector: 'app-admin-artist-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './admin-artist-form.component.html',
  styleUrl: './admin-artist-form.component.css'
})
export class AdminArtistFormComponent implements OnInit {
  artistForm: FormGroup;
  socialMediaLinks: ApiSocialMediaLink[] = [];
  isLoading = false;
  isSubmitting = false;
  error = '';
  isEditMode = false;
  artistId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private adminApi: AdminApiService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.artistForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      role: ['', [Validators.required]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      image: ['', [Validators.required]],
      social_media: this.fb.array([])
    });
  }

  ngOnInit(): void {
    this.loadSocialMediaLinks();
    
    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.artistId = +params['id'];
        this.loadArtist(this.artistId);
      }
    });
  }

  loadSocialMediaLinks(): void {
    this.adminApi.getSocialMediaLinks().subscribe({
      next: (links) => {
        this.socialMediaLinks = links;
        this.initializeSocialMediaForm();
      },
      error: (error) => {
        console.error('Error loading social media links:', error);
      }
    });
  }

  initializeSocialMediaForm(): void {
    const socialMediaArray = this.artistForm.get('social_media') as FormArray;
    this.socialMediaLinks.forEach(link => {
      socialMediaArray.push(this.fb.group({
        platform: [link.platform],
        url: [''],
        icon_class: [link.icon_class],
        color: [link.color],
        hover_color: [link.hover_color],
        name: [link.name]
      }));
    });
  }

  loadArtist(id: number): void {
    this.isLoading = true;
    this.adminApi.getArtist(id).subscribe({
      next: (artist) => {
        this.populateForm(artist);
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load artist';
        this.isLoading = false;
        console.error('Error loading artist:', error);
      }
    });
  }

  populateForm(artist: ApiArtist): void {
    this.artistForm.patchValue({
      name: artist.name,
      role: artist.role,
      description: artist.description,
      image: artist.image
    });

    // Populate social media
    const socialMediaArray = this.artistForm.get('social_media') as FormArray;
    socialMediaArray.controls.forEach((control, index) => {
      const platform = control.get('platform')?.value;
      const artistSocialMedia = artist.social_media?.find(sm => sm.platform === platform);
      if (artistSocialMedia) {
        control.patchValue({
          url: artistSocialMedia.url
        });
      }
    });
  }

  get socialMediaArray(): FormArray {
    return this.artistForm.get('social_media') as FormArray;
  }

  onSubmit(): void {
    if (this.artistForm.valid) {
      this.isSubmitting = true;
      this.error = '';

      const formData = this.prepareFormData();

      const operation = this.isEditMode 
        ? this.adminApi.updateArtist(this.artistId!, formData)
        : this.adminApi.createArtist(formData);

      operation.subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.router.navigate(['/admin/artists']);
        },
        error: (error) => {
          this.isSubmitting = false;
          this.error = 'Failed to save artist. Please try again.';
          console.error('Error saving artist:', error);
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  prepareFormData(): any {
    const formValue = this.artistForm.value;
    
    // Filter out empty social media URLs
    const socialMedia = formValue.social_media
      .filter((sm: any) => sm.url && sm.url.trim())
      .map((sm: any) => ({
        platform: sm.platform,
        url: sm.url,
        icon_class: sm.icon_class,
        color: sm.color,
        hover_color: sm.hover_color,
        name: sm.name
      }));

    return {
      name: formValue.name,
      role: formValue.role,
      description: formValue.description,
      image: formValue.image,
      social_media: socialMedia
    };
  }

  markFormGroupTouched(): void {
    Object.keys(this.artistForm.controls).forEach(key => {
      const control = this.artistForm.get(key);
      control?.markAsTouched();
    });
  }

  cancel(): void {
    this.router.navigate(['/admin/artists']);
  }

  onImageError(event: Event): void {
    const target = event.target as HTMLImageElement;
    if (target) {
      target.src = '/assets/placeholder-avatar.jpg';
    }
  }

  // Validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.artistForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.artistForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['minlength']) return `${fieldName} is too short`;
    }
    return '';
  }
}
