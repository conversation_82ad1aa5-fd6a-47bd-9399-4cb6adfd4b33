<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">
        {{ isEditMode ? 'Edit Artist' : 'Add New Artist' }}
      </h1>
      <p class="text-gray-600">
        {{ isEditMode ? 'Update artist information' : 'Add a new artist to your gallery' }}
      </p>
    </div>
    <button (click)="cancel()" 
            class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
      <i class="fas fa-arrow-left mr-2"></i>
      Back to Artists
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Artist Form -->
  <form *ngIf="!isLoading" [formGroup]="artistForm" (ngSubmit)="onSubmit()" class="space-y-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Information -->
      <div class="lg:col-span-2 space-y-6">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Artist Information</h3>
          
          <!-- Artist Name -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Artist Name *</label>
            <input
              type="text"
              formControlName="name"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('name')"
              placeholder="Enter artist name"
            />
            <div *ngIf="isFieldInvalid('name')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('name') }}
            </div>
          </div>

          <!-- Role -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Role/Title *</label>
            <input
              type="text"
              formControlName="role"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('role')"
              placeholder="e.g., Master Artist, Traditional Painter"
            />
            <div *ngIf="isFieldInvalid('role')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('role') }}
            </div>
          </div>

          <!-- Description -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Biography/Description *</label>
            <textarea
              formControlName="description"
              rows="6"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('description')"
              placeholder="Tell us about the artist's background, experience, and artistic style..."
            ></textarea>
            <div *ngIf="isFieldInvalid('description')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('description') }}
            </div>
          </div>

          <!-- Artist Image -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Profile Image URL *</label>
            <input
              type="url"
              formControlName="image"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('image')"
              placeholder="https://example.com/artist-photo.jpg"
            />
            <div *ngIf="isFieldInvalid('image')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('image') }}
            </div>
            <p class="mt-1 text-sm text-gray-500">Provide a URL to the artist's profile photo</p>
          </div>
        </div>

        <!-- Social Media Links -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Social Media Links</h3>
          <p class="text-sm text-gray-600 mb-4">Add the artist's social media profiles (optional)</p>
          
          <div formArrayName="social_media" class="space-y-4">
            <div *ngFor="let socialMedia of socialMediaArray.controls; let i = index" 
                 [formGroupName]="i" 
                 class="flex items-center space-x-3 p-3 border border-gray-200 rounded-md">
              
              <!-- Platform Icon and Name -->
              <div class="flex items-center space-x-2 w-32">
                <i [class]="socialMedia.get('icon_class')?.value" 
                   [style.color]="socialMedia.get('color')?.value"
                   class="text-lg"></i>
                <span class="text-sm font-medium text-gray-700">
                  {{ socialMedia.get('name')?.value }}
                </span>
              </div>
              
              <!-- URL Input -->
              <div class="flex-1">
                <input
                  type="url"
                  formControlName="url"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                  [placeholder]="'Enter ' + socialMedia.get('name')?.value + ' profile URL'"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Preview -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
          
          <div class="text-center">
            <!-- Artist Image Preview -->
            <div class="w-24 h-24 mx-auto mb-3 rounded-full overflow-hidden bg-gray-200">
              <img 
                *ngIf="artistForm.get('image')?.value" 
                [src]="artistForm.get('image')?.value" 
                [alt]="artistForm.get('name')?.value"
                class="w-full h-full object-cover"
                (error)="$event.target.src='/assets/placeholder-avatar.jpg'"
              />
              <div *ngIf="!artistForm.get('image')?.value" 
                   class="w-full h-full flex items-center justify-center text-gray-400">
                <i class="fas fa-user text-2xl"></i>
              </div>
            </div>
            
            <!-- Artist Name -->
            <h4 class="font-semibold text-gray-900">
              {{ artistForm.get('name')?.value || 'Artist Name' }}
            </h4>
            
            <!-- Artist Role -->
            <p class="text-sm text-orange-600 mb-2">
              {{ artistForm.get('role')?.value || 'Artist Role' }}
            </p>
            
            <!-- Description Preview -->
            <p class="text-sm text-gray-600 line-clamp-3">
              {{ artistForm.get('description')?.value || 'Artist description will appear here...' }}
            </p>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="space-y-3">
            <button
              type="submit"
              [disabled]="isSubmitting || artistForm.invalid"
              class="w-full flex items-center justify-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <span *ngIf="isSubmitting" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
              {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Artist' : 'Create Artist') }}
            </button>
            
            <button
              type="button"
              (click)="cancel()"
              class="w-full px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </div>

        <!-- Help -->
        <div class="bg-blue-50 rounded-lg p-4">
          <h4 class="text-sm font-semibold text-blue-900 mb-2">Tips</h4>
          <ul class="text-sm text-blue-800 space-y-1">
            <li>• Use high-quality images for better presentation</li>
            <li>• Write engaging biographies to connect with visitors</li>
            <li>• Social media links help visitors discover more work</li>
            <li>• Keep descriptions concise but informative</li>
          </ul>
        </div>
      </div>
    </div>
  </form>
</div>
