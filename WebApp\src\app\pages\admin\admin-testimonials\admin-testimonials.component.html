<div class="space-y-6">
  <!-- <PERSON>er -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Testimonials</h1>
      <p class="text-gray-600">Manage customer testimonials and reviews</p>
    </div>
    <a routerLink="/admin/testimonials/new" 
       class="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200">
      <i class="fas fa-plus mr-2"></i>
      Add Testimonial
    </a>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Testimonials Grid -->
  <div *ngIf="!isLoading && !error" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div *ngFor="let testimonial of testimonials" class="bg-white rounded-lg shadow-md p-6">
      <!-- Featured Badge -->
      <div class="flex items-center justify-between mb-4">
        <span *ngIf="testimonial.featured" 
              class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
          <i class="fas fa-star mr-1"></i>
          Featured
        </span>
        <div class="flex items-center space-x-2">
          <button (click)="toggleFeatured(testimonial)" 
                  [class]="'p-1 rounded ' + (testimonial.featured ? 'text-yellow-600 hover:text-yellow-700' : 'text-gray-400 hover:text-yellow-600')"
                  title="Toggle Featured">
            <i class="fas fa-star"></i>
          </button>
        </div>
      </div>
      
      <!-- Customer Info -->
      <div class="flex items-center mb-4">
        <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
          <i class="fas fa-user text-orange-600"></i>
        </div>
        <div class="ml-3">
          <h3 class="text-lg font-semibold text-gray-900">{{ testimonial.customer_name }}</h3>
          <p class="text-sm text-gray-600">{{ testimonial.customer_location }}</p>
        </div>
      </div>
      
      <!-- Rating -->
      <div class="flex items-center mb-3">
        <div class="flex items-center">
          <i *ngFor="let star of [1,2,3,4,5]" 
             [class]="'fas fa-star ' + (star <= testimonial.rating ? 'text-yellow-400' : 'text-gray-300')"></i>
        </div>
        <span class="ml-2 text-sm text-gray-600">({{ testimonial.rating }}/5)</span>
      </div>
      
      <!-- Testimonial Text -->
      <p class="text-gray-700 mb-4 line-clamp-4">{{ testimonial.content }}</p>
      
      <!-- Metadata -->
      <div class="text-xs text-gray-500 mb-4">
        Created: {{ testimonial.created_at | date:'short' }}
      </div>
      
      <!-- Actions -->
      <div class="flex items-center justify-between">
        <a [routerLink]="['/admin/testimonials', testimonial.id, 'edit']" 
           class="text-blue-600 hover:text-blue-700 text-sm font-medium">
          <i class="fas fa-edit mr-1"></i>
          Edit
        </a>
        <button (click)="deleteTestimonial(testimonial.id)" 
                class="text-red-600 hover:text-red-700 text-sm font-medium">
          <i class="fas fa-trash mr-1"></i>
          Delete
        </button>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && testimonials.length === 0" 
       class="text-center py-12">
    <i class="fas fa-star text-gray-400 text-6xl mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No testimonials found</h3>
    <p class="text-gray-600 mb-4">Get started by adding your first testimonial.</p>
    <a routerLink="/admin/testimonials/new" 
       class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200">
      <i class="fas fa-plus mr-2"></i>
      Add Testimonial
    </a>
  </div>
</div>
