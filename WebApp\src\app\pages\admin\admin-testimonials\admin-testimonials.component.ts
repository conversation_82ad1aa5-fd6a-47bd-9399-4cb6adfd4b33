import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { AdminApiService } from '../../../services/admin-api.service';

@Component({
  selector: 'app-admin-testimonials',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './admin-testimonials.component.html',
  styleUrl: './admin-testimonials.component.css'
})
export class AdminTestimonialsComponent implements OnInit {
  testimonials: any[] = [];
  isLoading = true;
  error = '';

  constructor(private adminApi: AdminApiService) {}

  ngOnInit(): void {
    this.loadTestimonials();
  }

  loadTestimonials(): void {
    this.isLoading = true;
    this.adminApi.getTestimonials().subscribe({
      next: (testimonials) => {
        this.testimonials = testimonials;
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load testimonials';
        this.isLoading = false;
        console.error('Testimonials error:', error);
      }
    });
  }

  deleteTestimonial(id: number): void {
    if (confirm('Are you sure you want to delete this testimonial?')) {
      this.adminApi.deleteTestimonial(id).subscribe({
        next: () => {
          this.loadTestimonials();
        },
        error: (error) => {
          console.error('Delete error:', error);
          alert('Failed to delete testimonial');
        }
      });
    }
  }

  toggleFeatured(testimonial: any): void {
    const updatedTestimonial = { ...testimonial, featured: !testimonial.featured };
    this.adminApi.updateTestimonial(testimonial.id, updatedTestimonial).subscribe({
      next: () => {
        this.loadTestimonials();
      },
      error: (error) => {
        console.error('Update error:', error);
        alert('Failed to update testimonial');
      }
    });
  }
}
