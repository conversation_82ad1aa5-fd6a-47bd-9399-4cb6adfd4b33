<div class="space-y-6">
  <!-- Page Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Contact Inquiries</h1>
      <p class="text-gray-600">Manage customer inquiries and support requests</p>
    </div>
    <button (click)="refreshInquiries()" 
            class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
      <i class="fas fa-sync-alt mr-2" [class.animate-spin]="isLoading"></i>
      Refresh
    </button>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Search -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Search Inquiries</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          placeholder="Search by name, email, subject, or message..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
        />
      </div>

      <!-- Status Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
        <select
          [(ngModel)]="selectedStatus"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
        >
          <option *ngFor="let status of statusOptions" [value]="status.value">
            {{ status.label }}
          </option>
        </select>
      </div>

      <!-- Results Count -->
      <div class="flex items-end">
        <div class="text-sm text-gray-600">
          Showing {{ filteredInquiries.length }} of {{ inquiries.length }} inquiries
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Inquiries List -->
  <div *ngIf="!isLoading && !error" class="space-y-4">
    <div *ngFor="let inquiry of filteredInquiries" class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
      <div class="flex items-start justify-between mb-4">
        <div class="flex-1">
          <div class="flex items-center space-x-3 mb-2">
            <h3 class="text-lg font-semibold text-gray-900">{{ inquiry.subject }}</h3>
            <select
              [value]="inquiry.status"
              (change)="updateInquiryStatus(inquiry.id!, $any($event.target).value)"
              class="text-xs font-semibold rounded-full px-3 py-1 border-0 focus:ring-2 focus:ring-orange-500"
              [class]="getStatusColor(inquiry.status || 'pending')"
            >
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>
          </div>
          
          <div class="flex items-center space-x-4 text-sm text-gray-600 mb-3">
            <div class="flex items-center">
              <i class="fas fa-user mr-1"></i>
              {{ inquiry.name }}
            </div>
            <div class="flex items-center">
              <i class="fas fa-envelope mr-1"></i>
              <a [href]="'mailto:' + inquiry.email" class="text-orange-600 hover:text-orange-700">
                {{ inquiry.email }}
              </a>
            </div>
            <div *ngIf="inquiry.phone" class="flex items-center">
              <i class="fas fa-phone mr-1"></i>
              <a [href]="'tel:' + inquiry.phone" class="text-orange-600 hover:text-orange-700">
                {{ inquiry.phone }}
              </a>
            </div>
            <div class="flex items-center">
              <i class="fas fa-calendar mr-1"></i>
              {{ inquiry.created_at | date:'short' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Message Content -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 mb-2">Message:</h4>
        <p class="text-sm text-gray-700 leading-relaxed">{{ inquiry.message }}</p>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
        <div class="flex space-x-3">
          <a 
            [href]="'mailto:' + inquiry.email + '?subject=Re: ' + inquiry.subject"
            class="inline-flex items-center px-3 py-2 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700 transition-colors duration-200"
          >
            <i class="fas fa-reply mr-2"></i>
            Reply via Email
          </a>
          
          <a 
            *ngIf="inquiry.phone"
            [href]="'tel:' + inquiry.phone"
            class="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors duration-200"
          >
            <i class="fas fa-phone mr-2"></i>
            Call
          </a>
        </div>
        
        <div class="text-xs text-gray-500">
          ID: #{{ inquiry.id }}
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && filteredInquiries.length === 0" class="text-center py-12">
    <i class="fas fa-envelope-open text-4xl text-gray-400 mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No inquiries found</h3>
    <p class="text-gray-500">
      {{ searchTerm || selectedStatus ? 'Try adjusting your filters' : 'No customer inquiries have been received yet' }}
    </p>
  </div>

  <!-- Summary Stats -->
  <div *ngIf="inquiries.length > 0" class="bg-white rounded-lg shadow-md p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Inquiry Statistics</h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="text-center">
        <div class="text-2xl font-bold text-yellow-600">
          {{ getPendingCount() }}
        </div>
        <div class="text-sm text-gray-600">Pending</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-blue-600">
          {{ getInProgressCount() }}
        </div>
        <div class="text-sm text-gray-600">In Progress</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-green-600">
          {{ getResolvedCount() }}
        </div>
        <div class="text-sm text-gray-600">Resolved</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-gray-600">
          {{ getTotalCount() }}
        </div>
        <div class="text-sm text-gray-600">Total</div>
      </div>
    </div>
  </div>
</div>
