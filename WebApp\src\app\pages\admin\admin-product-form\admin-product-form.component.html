<div class="space-y-6">
  <!-- <PERSON> Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">
        {{ isEditMode ? 'Edit Product' : 'Add New Product' }}
      </h1>
      <p class="text-gray-600">
        {{ isEditMode ? 'Update product information' : 'Create a new product for your catalog' }}
      </p>
    </div>
    <button (click)="cancel()" 
            class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
      <i class="fas fa-arrow-left mr-2"></i>
      Back to Products
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Product Form -->
  <form *ngIf="!isLoading" [formGroup]="productForm" (ngSubmit)="onSubmit()" class="space-y-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Information -->
      <div class="lg:col-span-2 space-y-6">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
          
          <!-- Product Name -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
            <input
              type="text"
              formControlName="name"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('name')"
              placeholder="Enter product name"
            />
            <div *ngIf="isFieldInvalid('name')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('name') }}
            </div>
          </div>

          <!-- Slug -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">URL Slug *</label>
            <input
              type="text"
              formControlName="slug"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('slug')"
              placeholder="product-url-slug"
            />
            <div *ngIf="isFieldInvalid('slug')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('slug') }}
            </div>
            <p class="mt-1 text-sm text-gray-500">This will be used in the product URL</p>
          </div>

          <!-- Description -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Short Description *</label>
            <textarea
              formControlName="description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('description')"
              placeholder="Brief description of the product"
            ></textarea>
            <div *ngIf="isFieldInvalid('description')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('description') }}
            </div>
          </div>

          <!-- Detailed Description -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Detailed Description</label>
            <textarea
              formControlName="detailed_description"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              placeholder="Detailed description of the product"
            ></textarea>
          </div>

          <!-- Cultural Significance -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Cultural Significance</label>
            <textarea
              formControlName="cultural_significance"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              placeholder="Cultural and historical significance of this artwork"
            ></textarea>
          </div>
        </div>

        <!-- Product Images -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Images</h3>
          
          <div formArrayName="images" class="space-y-3">
            <div *ngFor="let image of images.controls; let i = index" class="flex items-center space-x-3">
              <div class="flex-1">
                <input
                  type="url"
                  [formControlName]="i"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                  placeholder="https://example.com/image.jpg"
                />
              </div>
              <button
                type="button"
                (click)="removeImage(i)"
                [disabled]="images.length === 1"
                class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          
          <button
            type="button"
            (click)="addImage()"
            class="mt-3 flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <i class="fas fa-plus mr-2"></i>
            Add Image
          </button>
        </div>

        <!-- Materials -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Materials Used</h3>
          
          <div formArrayName="materials" class="space-y-3">
            <div *ngFor="let material of materials.controls; let i = index" class="flex items-center space-x-3">
              <div class="flex-1">
                <input
                  type="text"
                  [formControlName]="i"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                  placeholder="e.g., Natural pigments, Handmade paper"
                />
              </div>
              <button
                type="button"
                (click)="removeMaterial(i)"
                class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          
          <button
            type="button"
            (click)="addMaterial()"
            class="mt-3 flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <i class="fas fa-plus mr-2"></i>
            Add Material
          </button>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Product Settings -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Settings</h3>
          
          <!-- Category -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
            <select
              formControlName="category"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('category')"
            >
              <option value="">Select a category</option>
              <option *ngFor="let category of categories" [value]="category.id">
                {{ category.name }}
              </option>
            </select>
            <div *ngIf="isFieldInvalid('category')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('category') }}
            </div>
          </div>

          <!-- Price -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Price ($) *</label>
            <input
              type="number"
              formControlName="price"
              min="0"
              step="0.01"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              [class.border-red-500]="isFieldInvalid('price')"
              placeholder="0.00"
            />
            <div *ngIf="isFieldInvalid('price')" class="mt-1 text-sm text-red-600">
              {{ getFieldError('price') }}
            </div>
          </div>

          <!-- Dimensions -->
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Dimensions</label>
            <input
              type="text"
              formControlName="dimensions"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              placeholder="e.g., 12x16 inches"
            />
          </div>

          <!-- Featured -->
          <div class="mb-4">
            <label class="flex items-center">
              <input
                type="checkbox"
                formControlName="featured"
                class="rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"
              />
              <span class="ml-2 text-sm text-gray-700">Featured Product</span>
            </label>
            <p class="mt-1 text-sm text-gray-500">Featured products appear on the homepage</p>
          </div>
        </div>

        <!-- Artists -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Associated Artists</h3>
          
          <div class="space-y-2">
            <label *ngFor="let artist of artists" class="flex items-center">
              <input
                type="checkbox"
                [value]="artist.id"
                [checked]="isArtistSelected(artist.id)"
                (change)="onArtistChange($event, artist.id)"
                class="rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"
              />
              <span class="ml-2 text-sm text-gray-700">{{ artist.name }}</span>
            </label>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="space-y-3">
            <button
              type="submit"
              [disabled]="isSubmitting || productForm.invalid"
              class="w-full flex items-center justify-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <span *ngIf="isSubmitting" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
              {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Product' : 'Create Product') }}
            </button>
            
            <button
              type="button"
              (click)="cancel()"
              class="w-full px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
