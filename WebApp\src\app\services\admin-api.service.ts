import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { AdminAuthService } from './admin-auth.service';
import {
  ApiResponse, ApiArtist, ApiProduct, ApiGalleryImage, ApiFAQ,
  ApiTestimonial, ApiCompanyInfo, ApiArtistSpotlight, ApiTeamMember,
  ApiAchievement, ApiFacility, ApiOrder, ApiContactInquiry,
  ApiArtistApplication, ApiCategory, ApiSocialMediaLink,
  ApiCompanyFeature
} from './api.service';

export interface DashboardStats {
  total_products: number;
  total_artists: number;
  total_orders: number;
  pending_inquiries: number;
  pending_applications: number;
  total_gallery_images: number;
  total_testimonials: number;
  recent_orders: ApiOrder[];
  recent_inquiries: ApiContactInquiry[];
  recent_applications: ApiArtistApplication[];
}

@Injectable({
  providedIn: 'root'
})
export class AdminApiService {
  private baseUrl = environment.adminApiUrl;

  constructor(
    private http: HttpClient,
    private adminAuth: AdminAuthService
  ) {}

  private getHttpOptions() {
    return {
      headers: this.adminAuth.getAuthHeaders(),
      withCredentials: true
    };
  }

  // Dashboard
  getDashboardStats(): Observable<DashboardStats> {
    return this.http.get<DashboardStats>(`${this.baseUrl}/dashboard/stats/`, this.getHttpOptions());
  }

  // Social Media Links
  getSocialMediaLinks(): Observable<ApiSocialMediaLink[]> {
    return this.http.get<ApiSocialMediaLink[]>(`${this.baseUrl}/social-media-links/`, this.getHttpOptions());
  }

  createSocialMediaLink(data: Partial<ApiSocialMediaLink>): Observable<ApiSocialMediaLink> {
    return this.http.post<ApiSocialMediaLink>(`${this.baseUrl}/social-media-links/`, data, this.getHttpOptions());
  }

  updateSocialMediaLink(id: number, data: Partial<ApiSocialMediaLink>): Observable<ApiSocialMediaLink> {
    return this.http.put<ApiSocialMediaLink>(`${this.baseUrl}/social-media-links/${id}/`, data, this.getHttpOptions());
  }

  deleteSocialMediaLink(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/social-media-links/${id}/`, this.getHttpOptions());
  }

  // Artists
  getArtists(): Observable<ApiResponse<ApiArtist>> {
    return this.http.get<ApiResponse<ApiArtist>>(`${this.baseUrl}/artists/`, this.getHttpOptions());
  }

  getArtist(id: number): Observable<ApiArtist> {
    return this.http.get<ApiArtist>(`${this.baseUrl}/artists/${id}/`, this.getHttpOptions());
  }

  createArtist(data: Partial<ApiArtist>): Observable<ApiArtist> {
    return this.http.post<ApiArtist>(`${this.baseUrl}/artists/`, data, this.getHttpOptions());
  }

  updateArtist(id: number, data: Partial<ApiArtist>): Observable<ApiArtist> {
    return this.http.put<ApiArtist>(`${this.baseUrl}/artists/${id}/`, data, this.getHttpOptions());
  }

  deleteArtist(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/artists/${id}/`, this.getHttpOptions());
  }

  // Artist Spotlight
  getArtistSpotlights(): Observable<ApiResponse<ApiArtistSpotlight>> {
    return this.http.get<ApiResponse<ApiArtistSpotlight>>(`${this.baseUrl}/artist-spotlight/`, this.getHttpOptions());
  }

  createArtistSpotlight(data: Partial<ApiArtistSpotlight>): Observable<ApiArtistSpotlight> {
    return this.http.post<ApiArtistSpotlight>(`${this.baseUrl}/artist-spotlight/`, data, this.getHttpOptions());
  }

  updateArtistSpotlight(id: number, data: Partial<ApiArtistSpotlight>): Observable<ApiArtistSpotlight> {
    return this.http.put<ApiArtistSpotlight>(`${this.baseUrl}/artist-spotlight/${id}/`, data, this.getHttpOptions());
  }

  deleteArtistSpotlight(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/artist-spotlight/${id}/`, this.getHttpOptions());
  }

  // Team Members
  getTeamMembers(): Observable<ApiTeamMember[]> {
    return this.http.get<ApiResponse<ApiTeamMember>>(`${this.baseUrl}/team-members/`, this.getHttpOptions())
      .pipe(map(response => response.results || []));
  }

  createTeamMember(data: Partial<ApiTeamMember>): Observable<ApiTeamMember> {
    return this.http.post<ApiTeamMember>(`${this.baseUrl}/team-members/`, data, this.getHttpOptions());
  }

  updateTeamMember(id: number, data: Partial<ApiTeamMember>): Observable<ApiTeamMember> {
    return this.http.put<ApiTeamMember>(`${this.baseUrl}/team-members/${id}/`, data, this.getHttpOptions());
  }

  deleteTeamMember(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/team-members/${id}/`, this.getHttpOptions());
  }

  // Categories
  getCategories(): Observable<ApiResponse<ApiCategory>> {
    return this.http.get<ApiResponse<ApiCategory>>(`${this.baseUrl}/categories/`, this.getHttpOptions());
  }

  createCategory(data: Partial<ApiCategory>): Observable<ApiCategory> {
    return this.http.post<ApiCategory>(`${this.baseUrl}/categories/`, data, this.getHttpOptions());
  }

  updateCategory(id: number, data: Partial<ApiCategory>): Observable<ApiCategory> {
    return this.http.put<ApiCategory>(`${this.baseUrl}/categories/${id}/`, data, this.getHttpOptions());
  }

  deleteCategory(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/categories/${id}/`, this.getHttpOptions());
  }

  // Products
  getProducts(): Observable<ApiResponse<ApiProduct>> {
    return this.http.get<ApiResponse<ApiProduct>>(`${this.baseUrl}/products/`, this.getHttpOptions());
  }

  getProduct(id: number): Observable<ApiProduct> {
    return this.http.get<ApiProduct>(`${this.baseUrl}/products/${id}/`, this.getHttpOptions());
  }

  createProduct(data: Partial<ApiProduct>): Observable<ApiProduct> {
    return this.http.post<ApiProduct>(`${this.baseUrl}/products/`, data, this.getHttpOptions());
  }

  updateProduct(id: number, data: Partial<ApiProduct>): Observable<ApiProduct> {
    return this.http.put<ApiProduct>(`${this.baseUrl}/products/${id}/`, data, this.getHttpOptions());
  }

  deleteProduct(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/products/${id}/`, this.getHttpOptions());
  }

  // Gallery Images
  getGalleryImages(): Observable<ApiGalleryImage[]> {
    return this.http.get<ApiResponse<ApiGalleryImage>>(`${this.baseUrl}/gallery/`, this.getHttpOptions())
      .pipe(map(response => response.results || []));
  }

  createGalleryImage(data: Partial<ApiGalleryImage>): Observable<ApiGalleryImage> {
    return this.http.post<ApiGalleryImage>(`${this.baseUrl}/gallery/`, data, this.getHttpOptions());
  }

  updateGalleryImage(id: number, data: Partial<ApiGalleryImage>): Observable<ApiGalleryImage> {
    return this.http.put<ApiGalleryImage>(`${this.baseUrl}/gallery/${id}/`, data, this.getHttpOptions());
  }

  deleteGalleryImage(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/gallery/${id}/`, this.getHttpOptions());
  }

  // FAQ
  getFAQs(): Observable<ApiFAQ[]> {
    return this.http.get<ApiResponse<ApiFAQ>>(`${this.baseUrl}/faq/`, this.getHttpOptions())
      .pipe(map(response => response.results || []));
  }

  createFAQ(data: Partial<ApiFAQ>): Observable<ApiFAQ> {
    return this.http.post<ApiFAQ>(`${this.baseUrl}/faq/`, data, this.getHttpOptions());
  }

  updateFAQ(id: number, data: Partial<ApiFAQ>): Observable<ApiFAQ> {
    return this.http.put<ApiFAQ>(`${this.baseUrl}/faq/${id}/`, data, this.getHttpOptions());
  }

  deleteFAQ(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/faq/${id}/`, this.getHttpOptions());
  }

  // Testimonials
  getTestimonials(): Observable<ApiTestimonial[]> {
    return this.http.get<ApiResponse<ApiTestimonial>>(`${this.baseUrl}/testimonials/`, this.getHttpOptions())
      .pipe(map(response => response.results || []));
  }

  createTestimonial(data: Partial<ApiTestimonial>): Observable<ApiTestimonial> {
    return this.http.post<ApiTestimonial>(`${this.baseUrl}/testimonials/`, data, this.getHttpOptions());
  }

  updateTestimonial(id: number, data: Partial<ApiTestimonial>): Observable<ApiTestimonial> {
    return this.http.put<ApiTestimonial>(`${this.baseUrl}/testimonials/${id}/`, data, this.getHttpOptions());
  }

  deleteTestimonial(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/testimonials/${id}/`, this.getHttpOptions());
  }

  // Company Features
  getCompanyFeatures(): Observable<ApiCompanyFeature[]> {
    return this.http.get<ApiResponse<ApiCompanyFeature>>(`${this.baseUrl}/company-features/`, this.getHttpOptions())
      .pipe(map(response => response.results || []));
  }

  createCompanyFeature(data: Partial<ApiCompanyFeature>): Observable<ApiCompanyFeature> {
    return this.http.post<ApiCompanyFeature>(`${this.baseUrl}/company-features/`, data, this.getHttpOptions());
  }

  updateCompanyFeature(id: number, data: Partial<ApiCompanyFeature>): Observable<ApiCompanyFeature> {
    return this.http.put<ApiCompanyFeature>(`${this.baseUrl}/company-features/${id}/`, data, this.getHttpOptions());
  }

  deleteCompanyFeature(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/company-features/${id}/`, this.getHttpOptions());
  }

  // Company Info
  getCompanyInfo(): Observable<ApiCompanyInfo[]> {
    return this.http.get<ApiResponse<ApiCompanyInfo>>(`${this.baseUrl}/company-info/`, this.getHttpOptions())
      .pipe(map(response => response.results || []));
  }

  updateCompanyInfo(id: number, data: Partial<ApiCompanyInfo>): Observable<ApiCompanyInfo> {
    return this.http.put<ApiCompanyInfo>(`${this.baseUrl}/company-info/${id}/`, data, this.getHttpOptions());
  }

  // Achievements
  getAchievements(): Observable<ApiAchievement[]> {
    return this.http.get<ApiResponse<ApiAchievement>>(`${this.baseUrl}/achievements/`, this.getHttpOptions())
      .pipe(map(response => response.results || []));
  }

  createAchievement(data: Partial<ApiAchievement>): Observable<ApiAchievement> {
    return this.http.post<ApiAchievement>(`${this.baseUrl}/achievements/`, data, this.getHttpOptions());
  }

  updateAchievement(id: number, data: Partial<ApiAchievement>): Observable<ApiAchievement> {
    return this.http.put<ApiAchievement>(`${this.baseUrl}/achievements/${id}/`, data, this.getHttpOptions());
  }

  deleteAchievement(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/achievements/${id}/`, this.getHttpOptions());
  }

  // Facilities
  getFacilities(): Observable<ApiFacility[]> {
    return this.http.get<ApiResponse<ApiFacility>>(`${this.baseUrl}/facilities/`, this.getHttpOptions())
      .pipe(map(response => response.results || []));
  }

  createFacility(data: Partial<ApiFacility>): Observable<ApiFacility> {
    return this.http.post<ApiFacility>(`${this.baseUrl}/facilities/`, data, this.getHttpOptions());
  }

  updateFacility(id: number, data: Partial<ApiFacility>): Observable<ApiFacility> {
    return this.http.put<ApiFacility>(`${this.baseUrl}/facilities/${id}/`, data, this.getHttpOptions());
  }

  deleteFacility(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/facilities/${id}/`, this.getHttpOptions());
  }

  // Orders
  getOrders(): Observable<ApiResponse<ApiOrder>> {
    return this.http.get<ApiResponse<ApiOrder>>(`${this.baseUrl}/orders/`, this.getHttpOptions());
  }

  updateOrderStatus(id: number, status: string): Observable<ApiOrder> {
    return this.http.patch<ApiOrder>(`${this.baseUrl}/orders/${id}/update_status/`, { status }, this.getHttpOptions());
  }

  // Contact Inquiries
  getContactInquiries(): Observable<ApiResponse<ApiContactInquiry>> {
    return this.http.get<ApiResponse<ApiContactInquiry>>(`${this.baseUrl}/contact-inquiries/`, this.getHttpOptions());
  }

  updateInquiryStatus(id: number, status: string): Observable<ApiContactInquiry> {
    return this.http.patch<ApiContactInquiry>(`${this.baseUrl}/contact-inquiries/${id}/update_status/`, { status }, this.getHttpOptions());
  }

  // Artist Applications
  getArtistApplications(): Observable<ApiArtistApplication[]> {
    return this.http.get<ApiResponse<ApiArtistApplication>>(`${this.baseUrl}/artist-applications/`, this.getHttpOptions())
      .pipe(map(response => response.results || []));
  }

  updateArtistApplicationStatus(id: number, data: { status: string, admin_notes?: string }): Observable<ApiArtistApplication> {
    return this.http.patch<ApiArtistApplication>(`${this.baseUrl}/artist-applications/${id}/update_status/`, data, this.getHttpOptions());
  }

  deleteArtistApplication(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/artist-applications/${id}/`, this.getHttpOptions());
  }
}
