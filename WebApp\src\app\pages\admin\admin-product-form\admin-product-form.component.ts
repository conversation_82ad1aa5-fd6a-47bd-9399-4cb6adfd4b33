import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AdminApiService } from '../../../services/admin-api.service';
import { ApiProduct, ApiCategory, ApiArtist } from '../../../services/api.service';

@Component({
  selector: 'app-admin-product-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './admin-product-form.component.html',
  styleUrl: './admin-product-form.component.css'
})
export class AdminProductFormComponent implements OnInit {
  productForm: FormGroup;
  categories: ApiCategory[] = [];
  artists: ApiArtist[] = [];
  isLoading = false;
  isSubmitting = false;
  error = '';
  isEditMode = false;
  productId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private adminApi: AdminApiService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.productForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      slug: ['', [Validators.required]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      category: ['', [Validators.required]],
      price: ['', [Validators.required, Validators.min(0)]],
      images: this.fb.array([this.fb.control('', Validators.required)]),
      dimensions: [''],
      materials: this.fb.array([this.fb.control('')]),
      featured: [false],
      detailed_description: [''],
      cultural_significance: [''],
      artists: [[]]
    });
  }

  ngOnInit(): void {
    this.loadCategories();
    this.loadArtists();
    
    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.productId = +params['id'];
        this.loadProduct(this.productId);
      }
    });

    // Auto-generate slug from name
    this.productForm.get('name')?.valueChanges.subscribe(name => {
      if (name && !this.isEditMode) {
        const slug = this.generateSlug(name);
        this.productForm.patchValue({ slug }, { emitEvent: false });
      }
    });
  }

  loadCategories(): void {
    this.adminApi.getCategories().subscribe({
      next: (response) => {
        this.categories = response.results || [];
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });
  }

  loadArtists(): void {
    this.adminApi.getArtists().subscribe({
      next: (response) => {
        this.artists = response.results || [];
      },
      error: (error) => {
        console.error('Error loading artists:', error);
      }
    });
  }

  loadProduct(id: number): void {
    this.isLoading = true;
    this.adminApi.getProduct(id).subscribe({
      next: (product) => {
        this.populateForm(product);
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load product';
        this.isLoading = false;
        console.error('Error loading product:', error);
      }
    });
  }

  populateForm(product: ApiProduct): void {
    // Clear existing form arrays
    this.clearFormArray('images');
    this.clearFormArray('materials');

    // Populate images
    product.images.forEach(image => {
      this.addImage(image);
    });

    // Populate materials
    product.materials.forEach(material => {
      this.addMaterial(material);
    });

    // Populate form
    this.productForm.patchValue({
      name: product.name,
      slug: product.slug,
      description: product.description,
      category: product.category.id,
      price: product.price,
      dimensions: product.dimensions,
      featured: product.featured,
      detailed_description: product.detailed_description,
      cultural_significance: product.cultural_significance,
      artists: product.artists?.map(a => a.id) || []
    });
  }

  generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  // Form Array Helpers
  get images(): FormArray {
    return this.productForm.get('images') as FormArray;
  }

  get materials(): FormArray {
    return this.productForm.get('materials') as FormArray;
  }

  addImage(value: string = ''): void {
    this.images.push(this.fb.control(value, Validators.required));
  }

  removeImage(index: number): void {
    if (this.images.length > 1) {
      this.images.removeAt(index);
    }
  }

  addMaterial(value: string = ''): void {
    this.materials.push(this.fb.control(value));
  }

  removeMaterial(index: number): void {
    this.materials.removeAt(index);
  }

  clearFormArray(arrayName: string): void {
    const formArray = this.productForm.get(arrayName) as FormArray;
    while (formArray.length !== 0) {
      formArray.removeAt(0);
    }
  }

  onSubmit(): void {
    if (this.productForm.valid) {
      this.isSubmitting = true;
      this.error = '';

      const formData = this.prepareFormData();

      const operation = this.isEditMode 
        ? this.adminApi.updateProduct(this.productId!, formData)
        : this.adminApi.createProduct(formData);

      operation.subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.router.navigate(['/admin/products']);
        },
        error: (error) => {
          this.isSubmitting = false;
          this.error = 'Failed to save product. Please try again.';
          console.error('Error saving product:', error);
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  prepareFormData(): any {
    const formValue = this.productForm.value;
    return {
      name: formValue.name,
      slug: formValue.slug,
      description: formValue.description,
      category: formValue.category,
      price: parseFloat(formValue.price),
      images: formValue.images.filter((img: string) => img.trim()),
      dimensions: formValue.dimensions,
      materials: formValue.materials.filter((mat: string) => mat.trim()),
      featured: formValue.featured,
      detailed_description: formValue.detailed_description,
      cultural_significance: formValue.cultural_significance
    };
  }

  markFormGroupTouched(): void {
    Object.keys(this.productForm.controls).forEach(key => {
      const control = this.productForm.get(key);
      control?.markAsTouched();
    });
  }

  cancel(): void {
    this.router.navigate(['/admin/products']);
  }

  // Artist selection handler
  onArtistChange(event: any, artistId: number): void {
    const currentArtists = this.productForm.get('artists')?.value || [];
    if (event.target.checked) {
      if (!currentArtists.includes(artistId)) {
        this.productForm.patchValue({
          artists: [...currentArtists, artistId]
        });
      }
    } else {
      this.productForm.patchValue({
        artists: currentArtists.filter((id: number) => id !== artistId)
      });
    }
  }

  // Check if artist is selected
  isArtistSelected(artistId: number): boolean {
    const currentArtists = this.productForm.get('artists')?.value || [];
    return currentArtists.includes(artistId);
  }

  // Validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.productForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.productForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['minlength']) return `${fieldName} is too short`;
      if (field.errors['min']) return `${fieldName} must be greater than 0`;
    }
    return '';
  }
}
