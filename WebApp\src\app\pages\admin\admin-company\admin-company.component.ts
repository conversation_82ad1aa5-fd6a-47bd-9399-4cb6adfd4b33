import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { AdminApiService } from '../../../services/admin-api.service';

@Component({
  selector: 'app-admin-company',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './admin-company.component.html',
  styleUrl: './admin-company.component.css'
})
export class AdminCompanyComponent implements OnInit {
  companyInfo: any = null;
  companyFeatures: any[] = [];
  achievements: any[] = [];
  facilities: any[] = [];
  isLoading = true;
  error = '';

  constructor(private adminApi: AdminApiService) {}

  ngOnInit(): void {
    this.loadCompanyData();
  }

  loadCompanyData(): void {
    this.isLoading = true;
    
    // Load all company-related data
    Promise.all([
      this.adminApi.getCompanyInfo().toPromise(),
      this.adminApi.getCompanyFeatures().toPromise(),
      this.adminApi.getAchievements().toPromise(),
      this.adminApi.getFacilities().toPromise()
    ]).then(([companyInfo, features, achievements, facilities]) => {
      this.companyInfo = companyInfo?.[0] || null;
      this.companyFeatures = features || [];
      this.achievements = achievements || [];
      this.facilities = facilities || [];
      this.isLoading = false;
    }).catch(error => {
      this.error = 'Failed to load company data';
      this.isLoading = false;
      console.error('Company data error:', error);
    });
  }

  deleteFeature(id: number): void {
    if (confirm('Are you sure you want to delete this feature?')) {
      this.adminApi.deleteCompanyFeature(id).subscribe({
        next: () => {
          this.loadCompanyData();
        },
        error: (error) => {
          console.error('Delete error:', error);
          alert('Failed to delete feature');
        }
      });
    }
  }

  deleteAchievement(id: number): void {
    if (confirm('Are you sure you want to delete this achievement?')) {
      this.adminApi.deleteAchievement(id).subscribe({
        next: () => {
          this.loadCompanyData();
        },
        error: (error) => {
          console.error('Delete error:', error);
          alert('Failed to delete achievement');
        }
      });
    }
  }

  deleteFacility(id: number): void {
    if (confirm('Are you sure you want to delete this facility?')) {
      this.adminApi.deleteFacility(id).subscribe({
        next: () => {
          this.loadCompanyData();
        },
        error: (error) => {
          console.error('Delete error:', error);
          alert('Failed to delete facility');
        }
      });
    }
  }
}
