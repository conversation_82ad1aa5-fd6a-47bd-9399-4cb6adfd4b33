<div class="space-y-6">
  <!-- Page Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Gallery Management</h1>
      <p class="text-gray-600">Manage your gallery images and artwork</p>
    </div>
    <div class="flex space-x-3">
      <button (click)="refreshImages()" 
              class="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200">
        <i class="fas fa-sync-alt mr-2" [class.animate-spin]="isLoading"></i>
        Refresh
      </button>
      <a routerLink="/admin/gallery/new" 
         class="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200">
        <i class="fas fa-plus mr-2"></i>
        Add Image
      </a>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Search -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Search Images</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          placeholder="Search by title, artist, or description..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
        />
      </div>

      <!-- Category Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
        <select
          [(ngModel)]="selectedCategory"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500"
        >
          <option *ngFor="let category of categories" [value]="category === 'All' ? '' : category">
            {{ category }}
          </option>
        </select>
      </div>

      <!-- Results Count -->
      <div class="flex items-end">
        <div class="text-sm text-gray-600">
          Showing {{ filteredImages.length }} of {{ galleryImages.length }} images
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
    {{ error }}
  </div>

  <!-- Gallery Grid -->
  <div *ngIf="!isLoading && !error" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    <div *ngFor="let image of filteredImages" class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
      <!-- Image -->
      <div class="aspect-square bg-gray-200 overflow-hidden">
        <img 
          [src]="image.image" 
          [alt]="image.title"
          class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
          (error)="onImageError($event)"
        />
      </div>
      
      <!-- Image Info -->
      <div class="p-4">
        <div class="flex items-start justify-between mb-2">
          <div class="flex-1 min-w-0">
            <h3 class="text-sm font-semibold text-gray-900 truncate">{{ image.title }}</h3>
            <p class="text-xs text-gray-600">by {{ image.artist }}</p>
          </div>
          <div class="flex space-x-1 ml-2">
            <a 
              [routerLink]="['/admin/gallery', image.id, 'edit']"
              class="text-orange-600 hover:text-orange-900 p-1 rounded-md hover:bg-orange-50"
              title="Edit Image"
            >
              <i class="fas fa-edit text-sm"></i>
            </a>
            <button
              (click)="deleteImage(image.id)"
              class="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-red-50"
              title="Delete Image"
            >
              <i class="fas fa-trash text-sm"></i>
            </button>
          </div>
        </div>
        
        <!-- Category Badge -->
        <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 mb-2">
          {{ image.category }}
        </span>
        
        <!-- Description -->
        <p class="text-xs text-gray-600 line-clamp-2">{{ image.description }}</p>
        
        <!-- Metadata -->
        <div class="text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
          <div>Added: {{ image.created_at | date:'short' }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && filteredImages.length === 0" class="text-center py-12">
    <i class="fas fa-images text-4xl text-gray-400 mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No images found</h3>
    <p class="text-gray-500 mb-4">
      {{ searchTerm || selectedCategory ? 'Try adjusting your filters' : 'Get started by adding your first gallery image' }}
    </p>
    <a *ngIf="!searchTerm && !selectedCategory" 
       routerLink="/admin/gallery/new"
       class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors duration-200">
      <i class="fas fa-plus mr-2"></i>
      Add First Image
    </a>
  </div>

  <!-- Bulk Actions (Future Enhancement) -->
  <div *ngIf="filteredImages.length > 0" class="bg-white rounded-lg shadow-md p-4">
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-600">
        Total: {{ galleryImages.length }} images
      </div>
      <div class="flex space-x-2">
        <button class="text-sm text-gray-600 hover:text-gray-900 px-3 py-1 rounded-md hover:bg-gray-100">
          <i class="fas fa-download mr-1"></i>
          Export List
        </button>
        <button class="text-sm text-gray-600 hover:text-gray-900 px-3 py-1 rounded-md hover:bg-gray-100">
          <i class="fas fa-filter mr-1"></i>
          Advanced Filters
        </button>
      </div>
    </div>
  </div>
</div>
