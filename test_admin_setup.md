# Admin Setup and Testing Guide

## 1. Create Admin User

Run the following command in the Django backend directory:

```bash
cd mithlani_backend
python manage.py create_admin_user --username admin --password admin123 --email <EMAIL> --full-name "Admin User"
```

## 2. Start the Backend Server

```bash
python manage.py runserver
```

## 3. Start the Frontend Server

```bash
cd WebApp
ng serve
```

## 4. Test Admin Login

1. Navigate to `http://localhost:4200/admin/login`
2. Login with:
   - Username: `admin`
   - Password: `admin123`

## 5. Test Admin Functionality

### Dashboard
- ✅ Dashboard statistics should load
- ✅ Navigation sidebar should be visible
- ✅ All navigation links should be clickable

### Navigation Links to Test:
- ✅ Dashboard (`/admin/dashboard`)
- ✅ Products (`/admin/products`)
- ✅ Artists (`/admin/artists`)
- ✅ Gallery (`/admin/gallery`)
- ✅ Team Members (`/admin/team`)
- ✅ Orders (`/admin/orders`)
- ✅ Inquiries (`/admin/inquiries`)
- ✅ Applications (`/admin/applications`)
- ✅ Company Info (`/admin/company`)
- ✅ FAQ (`/admin/faq`)
- ✅ Testimonials (`/admin/testimonials`)

### Authentication Features:
- ✅ Login with cookies
- ✅ Session verification
- ✅ Logout functionality
- ✅ Protected routes (redirect to login if not authenticated)

### API Security:
- ✅ Admin API endpoints require authentication
- ✅ Public API endpoints allow read access
- ✅ Form submission endpoints (orders, inquiries, applications) allow public create access

## 6. Issues Fixed

1. **Artist Spotlight API Error**: Fixed null checks in data.service.ts
2. **Admin Authentication**: Implemented custom permission class with session-based auth
3. **Cookie-based Authentication**: Added HTTP-only cookies for better security
4. **Missing Admin Pages**: Created all missing admin components and routes
5. **API Permissions**: Protected admin endpoints while keeping public endpoints accessible
6. **Dashboard Statistics**: Fixed authentication for dashboard stats API

## 7. Admin User Management

The admin user system uses:
- SHA256 hashed passwords
- Session-based authentication with tokens
- HTTP-only cookies for security
- 24-hour session expiration
- Admin user roles and permissions

## 8. Next Steps

1. Test all CRUD operations in each admin section
2. Verify form submissions work correctly
3. Test file uploads for images
4. Verify data persistence
5. Test responsive design on mobile devices
