import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AdminAuthService } from '../services/admin-auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminAuthGuard implements CanActivate {

  constructor(
    private adminAuth: AdminAuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    // Check if user is logged in
    if (!this.adminAuth.isLoggedIn) {
      console.log('Admin guard: User not logged in, redirecting to login');
      this.router.navigate(['/admin/login']);
      return false;
    }

    // Verify session is still valid
    console.log('Admin guard: Verifying session...');
    return this.adminAuth.verifySession().pipe(
      map(() => {
        console.log('Admin guard: Session valid, allowing access');
        return true;
      }),
      catchError((error) => {
        // Session is invalid, redirect to login
        console.log('Admin guard: Session invalid, redirecting to login', error);
        this.adminAuth.clearSession();
        this.router.navigate(['/admin/login']);
        return of(false);
      })
    );
  }
}
