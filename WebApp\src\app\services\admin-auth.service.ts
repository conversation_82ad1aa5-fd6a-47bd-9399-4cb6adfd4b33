import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface AdminUser {
  id: number;
  username: string;
  email: string;
  full_name: string;
  is_active: boolean;
  is_super_admin: boolean;
  last_login: string;
  created_at: string;
}

export interface AdminSession {
  session_token: string;
  expires_at: string;
  admin_user: AdminUser;
  created_at: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  session_token: string;
  expires_at: string;
  admin_user: AdminUser;
}

@Injectable({
  providedIn: 'root'
})
export class AdminAuthService {
  private baseUrl = environment.adminApiUrl;
  private currentUserSubject = new BehaviorSubject<AdminUser | null>(null);
  private sessionTokenSubject = new BehaviorSubject<string | null>(null);
  private accessTokenSubject = new BehaviorSubject<string | null>(null);
  private refreshTokenSubject = new BehaviorSubject<string | null>(null);

  public currentUser$ = this.currentUserSubject.asObservable();
  public sessionToken$ = this.sessionTokenSubject.asObservable();
  public accessToken$ = this.accessTokenSubject.asObservable();

  constructor(private http: HttpClient) {
    // Check for existing session on service initialization
    this.checkStoredSession();
  }

  private checkStoredSession(): void {
    const accessToken = localStorage.getItem('admin_access_token');
    const refreshToken = localStorage.getItem('admin_refresh_token');
    const sessionToken = localStorage.getItem('admin_session_token');
    const userStr = localStorage.getItem('admin_user');

    if ((accessToken || sessionToken) && userStr) {
      try {
        const user = JSON.parse(userStr);
        this.accessTokenSubject.next(accessToken);
        this.refreshTokenSubject.next(refreshToken);
        this.sessionTokenSubject.next(sessionToken);
        this.currentUserSubject.next(user);

        // Verify the session is still valid
        this.verifySession().subscribe({
          next: (session) => {
            // Session is valid, update user data
            this.currentUserSubject.next(session.admin_user);
          },
          error: () => {
            // Session is invalid, try to refresh token
            if (refreshToken) {
              this.refreshAccessToken().subscribe({
                next: () => {
                  console.log('Token refreshed successfully');
                },
                error: () => {
                  // Refresh failed, clear stored data
                  this.clearSession();
                }
              });
            } else {
              this.clearSession();
            }
          }
        });
      } catch (error) {
        // Invalid stored data, clear it
        this.clearSession();
      }
    }
  }

  login(credentials: LoginCredentials): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${this.baseUrl}/auth/login/`, credentials, { withCredentials: true })
      .pipe(
        tap(response => {
          // Store all tokens and user data
          localStorage.setItem('admin_access_token', response.access_token);
          localStorage.setItem('admin_refresh_token', response.refresh_token);
          localStorage.setItem('admin_session_token', response.session_token);
          localStorage.setItem('admin_user', JSON.stringify(response.admin_user));

          // Update subjects
          this.accessTokenSubject.next(response.access_token);
          this.refreshTokenSubject.next(response.refresh_token);
          this.sessionTokenSubject.next(response.session_token);
          this.currentUserSubject.next(response.admin_user);
        }),
        catchError(this.handleError)
      );
  }

  logout(): Observable<any> {
    const token = this.sessionTokenSubject.value;
    const headers = token ? new HttpHeaders().set('Authorization', `Bearer ${token}`) : undefined;

    return this.http.post(`${this.baseUrl}/auth/logout/`, {}, { headers, withCredentials: true })
      .pipe(
        tap(() => {
          this.clearSession();
        }),
        catchError(this.handleError)
      );
  }

  verifySession(): Observable<AdminSession> {
    const token = this.sessionTokenSubject.value;
    if (!token) {
      return throwError(() => new Error('No session token'));
    }

    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.get<AdminSession>(`${this.baseUrl}/auth/verify/`, { headers, withCredentials: true })
      .pipe(
        catchError(this.handleError)
      );
  }

  public clearSession(): void {
    localStorage.removeItem('admin_access_token');
    localStorage.removeItem('admin_refresh_token');
    localStorage.removeItem('admin_session_token');
    localStorage.removeItem('admin_user');
    this.accessTokenSubject.next(null);
    this.refreshTokenSubject.next(null);
    this.sessionTokenSubject.next(null);
    this.currentUserSubject.next(null);
  }

  private handleError(error: any): Observable<never> {
    console.error('Admin Auth Error:', error);
    return throwError(() => error);
  }

  // Utility methods
  get isLoggedIn(): boolean {
    const hasAccessToken = this.accessTokenSubject.value !== null;
    const hasSessionToken = this.sessionTokenSubject.value !== null;
    const hasUser = this.currentUserSubject.value !== null;
    const result = (hasAccessToken || hasSessionToken) && hasUser;
    console.log('Admin Auth: isLoggedIn check', { hasAccessToken, hasSessionToken, hasUser, result });
    return result;
  }

  get currentUser(): AdminUser | null {
    return this.currentUserSubject.value;
  }

  get sessionToken(): string | null {
    return this.sessionTokenSubject.value;
  }

  getAuthHeaders(): HttpHeaders {
    const accessToken = this.accessTokenSubject.value;
    const sessionToken = this.sessionToken;
    const token = accessToken || sessionToken;

    return token ? new HttpHeaders().set('Authorization', `Bearer ${token}`) : new HttpHeaders();
  }

  refreshAccessToken(): Observable<LoginResponse> {
    const refreshToken = this.refreshTokenSubject.value;
    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    return this.http.post<LoginResponse>(`${this.baseUrl}/auth/refresh/`,
      { refresh_token: refreshToken },
      { withCredentials: true }
    ).pipe(
      tap(response => {
        // Update stored tokens
        localStorage.setItem('admin_access_token', response.access_token);
        localStorage.setItem('admin_refresh_token', response.refresh_token);
        localStorage.setItem('admin_user', JSON.stringify(response.admin_user));

        // Update subjects
        this.accessTokenSubject.next(response.access_token);
        this.refreshTokenSubject.next(response.refresh_token);
        this.currentUserSubject.next(response.admin_user);
      }),
      catchError(this.handleError)
    );
  }

  // Check if session is expired
  isSessionExpired(): boolean {
    const userStr = localStorage.getItem('admin_user');
    if (!userStr) return true;

    try {
      // You might want to store expiry time separately and check it here
      return false; // For now, assume session is valid if user data exists
    } catch {
      return true;
    }
  }
}
