import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AdminApiService } from '../../../services/admin-api.service';

@Component({
  selector: 'app-admin-applications',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './admin-applications.component.html',
  styleUrl: './admin-applications.component.css'
})
export class AdminApplicationsComponent implements OnInit {
  applications: any[] = [];
  isLoading = true;
  error = '';
  selectedStatus = 'all';

  statusOptions = [
    { value: 'all', label: 'All Applications' },
    { value: 'pending', label: 'Pending' },
    { value: 'under_review', label: 'Under Review' },
    { value: 'approved', label: 'Approved' },
    { value: 'rejected', label: 'Rejected' }
  ];

  constructor(private adminApi: AdminApiService) {}

  ngOnInit(): void {
    this.loadApplications();
  }

  loadApplications(): void {
    this.isLoading = true;
    this.adminApi.getArtistApplications().subscribe({
      next: (applications) => {
        this.applications = applications;
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load applications';
        this.isLoading = false;
        console.error('Applications error:', error);
      }
    });
  }

  get filteredApplications() {
    if (this.selectedStatus === 'all') {
      return this.applications;
    }
    return this.applications.filter(app => app.status === this.selectedStatus);
  }

  updateApplicationStatus(id: number, status: string): void {
    const adminNotes = prompt('Add admin notes (optional):') || '';
    
    this.adminApi.updateArtistApplicationStatus(id, { status, admin_notes: adminNotes }).subscribe({
      next: () => {
        this.loadApplications();
      },
      error: (error) => {
        console.error('Update error:', error);
        alert('Failed to update application status');
      }
    });
  }

  deleteApplication(id: number): void {
    if (confirm('Are you sure you want to delete this application?')) {
      this.adminApi.deleteArtistApplication(id).subscribe({
        next: () => {
          this.loadApplications();
        },
        error: (error) => {
          console.error('Delete error:', error);
          alert('Failed to delete application');
        }
      });
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'under_review': return 'bg-blue-100 text-blue-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }
}
