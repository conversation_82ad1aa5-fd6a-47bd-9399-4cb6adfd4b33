import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AdminApiService } from '../../../services/admin-api.service';
import { ApiGalleryImage } from '../../../services/api.service';

@Component({
  selector: 'app-admin-gallery',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './admin-gallery.component.html',
  styleUrl: './admin-gallery.component.css'
})
export class AdminGalleryComponent implements OnInit {
  galleryImages: ApiGalleryImage[] = [];
  isLoading = true;
  error = '';
  searchTerm = '';
  selectedCategory = '';
  
  categories = ['All', 'Paintings', 'Clay Crafts', 'Textiles', 'Wood Crafts', 'Sculptures'];

  constructor(private adminApi: AdminApiService) {}

  ngOnInit(): void {
    this.loadGalleryImages();
  }

  loadGalleryImages(): void {
    this.isLoading = true;
    this.adminApi.getGalleryImages().subscribe({
      next: (images) => {
        this.galleryImages = images || [];
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Failed to load gallery images';
        this.isLoading = false;
        console.error('Gallery images error:', error);
      }
    });
  }

  deleteImage(id: number): void {
    if (confirm('Are you sure you want to delete this image?')) {
      this.adminApi.deleteGalleryImage(id).subscribe({
        next: () => {
          this.galleryImages = this.galleryImages.filter(img => img.id !== id);
        },
        error: (error) => {
          alert('Failed to delete image');
          console.error('Delete error:', error);
        }
      });
    }
  }

  get filteredImages(): ApiGalleryImage[] {
    return this.galleryImages.filter(image => {
      const matchesSearch = !this.searchTerm || 
        image.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        image.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        image.artist.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesCategory = this.selectedCategory === 'All' || !this.selectedCategory || 
        image.category === this.selectedCategory;
      
      return matchesSearch && matchesCategory;
    });
  }

  refreshImages(): void {
    this.loadGalleryImages();
  }

  onImageError(event: Event): void {
    const target = event.target as HTMLImageElement;
    if (target) {
      target.src = '/assets/placeholder-image.jpg';
    }
  }
}
